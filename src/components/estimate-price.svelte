<script lang="ts">
	import { _ } from 'svelte-i18n';

	let activeTab = 'intercity';
	let packageType = 'packet';
	let packageTypeIntra = 'packet';
	let packing = 'dont_want';
	let packingIntra = 'dont_want';

	function setTab(tab: string) {
		activeTab = tab;
	}

	$: showDimensions = packageType === 'package';
	$: showDimensionsIntra = packageTypeIntra === 'package';
	$: showPackingType = packing === 'want';
	$: showPackingTypeIntra = packingIntra === 'want';
</script>

<div class="container mx-auto p-4">
	<h3>{$_('estimate_price.title')}</h3>

	<!-- Intercity Tab Content -->
	{#if activeTab === 'intercity'}
		<div>
			<div class="mb-4">
				<label class="block text-gray-700">مبدا:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option value="1">تهران</option>
					<option value="2">اصفهان</option>
					<option value="3">شیراز</option>
				</select>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">مقصد:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option value="1">تهران</option>
					<option value="2">اصفهان</option>
					<option value="3">شیراز</option>
				</select>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">{$_('estimate_price.package_type')}:</label>
				<div class="flex space-x-4">
					<label
						><input type="radio" bind:group={packageType} value="packet" />
						{$_('estimate_price.packet')}</label
					>
					<label
						><input type="radio" bind:group={packageType} value="mini-pack" />
						{$_('estimate_price.mini_pack')}</label
					>
					<label
						><input type="radio" bind:group={packageType} value="package" />
						{$_('estimate_price.package')}</label
					>
				</div>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">{$_('estimate_price.weight_kg')}:</label>
				<input type="number" class="w-full rounded border p-2" min="0.1" step="0.1" />
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">{$_('estimate_price.value_toman')}:</label>
				<input type="number" class="w-full rounded border p-2" min="2000000" />
			</div>
			{#if showDimensions}
				<div class="mb-4">
					<div class="flex space-x-4">
						<div>
							<label class="block text-gray-700">طول (سانتی‌متر):</label>
							<input type="number" class="w-full rounded border p-2" min="1" max="200" />
						</div>
						<div>
							<label class="block text-gray-700">عرض (سانتی‌متر):</label>
							<input type="number" class="w-full rounded border p-2" min="1" max="200" />
						</div>
						<div>
							<label class="block text-gray-700">ارتفاع (سانتی‌متر):</label>
							<input type="number" class="w-full rounded border p-2" min="1" max="200" />
						</div>
					</div>
				</div>
			{/if}
			<div class="mb-4">
				<label class="block text-gray-700">بسته بندی:</label>
				<div class="flex space-x-4">
					<label><input type="radio" bind:group={packing} value="want" /> می‌خواهم</label>
					<label><input type="radio" bind:group={packing} value="dont_want" /> نمی‌خواهم</label>
				</div>
			</div>
			{#if showPackingType}
				<div class="mb-4">
					<label class="block text-gray-700">نوع بسته بندی:</label>
					<select class="w-full rounded border p-2">
						<option>انتخاب کنید</option>
						<option value="1">جعبه</option>
						<option value="2">پاکت مقاوم</option>
					</select>
				</div>
			{/if}
			<div class="mb-4">
				<label class="block text-gray-700">نوع پرداخت:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option>نقدی</option>
					<option>پس کرایه</option>
				</select>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">نوع جمع آوری:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option>در پلاک مبدا</option>
					<option>در محل نمایندگی</option>
				</select>
			</div>
			<div class="text-center">
				<button class="rounded bg-[#c9262c] px-4 py-2 text-white">برآورد قیمت و زمان حمل</button>
			</div>
		</div>
	{/if}

	<!-- Intracity Tab Content -->
	{#if activeTab === 'intracity'}
		<div>
			<div class="mb-4">
				<label class="block text-gray-700">شهر:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option value="1">تهران</option>
					<option value="2">اصفهان</option>
					<option value="3">شیراز</option>
				</select>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">{$_('estimate_price.package_type')}:</label>
				<div class="flex space-x-4">
					<label><input type="radio" bind:group={packageTypeIntra} value="packet" /> {$_('estimate_price.packet')}</label>
					<label
						><input type="radio" bind:group={packageTypeIntra} value="mini-pack" /> {$_('estimate_price.mini_pack')}</label
					>
					<label><input type="radio" bind:group={packageTypeIntra} value="package" /> {$_('estimate_price.package')}</label>
				</div>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">{$_('estimate_price.weight_kg')}:</label>
				<input type="number" class="w-full rounded border p-2" min="0.1" step="0.1" />
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">{$_('estimate_price.value_rial')}:</label>
				<input type="number" class="w-full rounded border p-2" min="2000000" />
			</div>
			{#if showDimensionsIntra}
				<div class="mb-4">
					<div class="flex space-x-4">
						<div>
							<label class="block text-gray-700">طول (سانتی‌متر):</label>
							<input type="number" class="w-full rounded border p-2" min="1" max="200" />
						</div>
						<div>
							<label class="block text-gray-700">عرض (سانتی‌متر):</label>
							<input type="number" class="w-full rounded border p-2" min="1" max="200" />
						</div>
						<div>
							<label class="block text-gray-700">ارتفاع (سانتی‌متر):</label>
							<input type="number" class="w-full rounded border p-2" min="1" max="200" />
						</div>
					</div>
				</div>
			{/if}
			<div class="mb-4">
				<label class="block text-gray-700">بسته بندی:</label>
				<div class="flex space-x-4">
					<label><input type="radio" bind:group={packingIntra} value="want" /> می‌خواهم</label>
					<label><input type="radio" bind:group={packingIntra} value="dont_want" /> نمی‌خواهم</label
					>
				</div>
			</div>
			{#if showPackingTypeIntra}
				<div class="mb-4">
					<label class="block text-gray-700">نوع بسته بندی:</label>
					<select class="w-full rounded border p-2">
						<option>انتخاب کنید</option>
						<option value="1">جعبه</option>
						<option value="2">پاکت مقاوم</option>
					</select>
				</div>
			{/if}
			<div class="mb-4">
				<label class="block text-gray-700">نوع پرداخت:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option>نقدی</option>
					<option>پس کرایه</option>
				</select>
			</div>
			<div class="mb-4">
				<label class="block text-gray-700">نوع جمع آوری:</label>
				<select class="w-full rounded border p-2">
					<option>انتخاب کنید</option>
					<option>در پلاک مبدا</option>
					<option>در محل نمایندگی</option>
				</select>
			</div>
			<div class="text-center">
				<button class="rounded bg-blue-500 px-4 py-2 text-white">برآورد قیمت و زمان حمل</button>
			</div>
		</div>
	{/if}
</div>
