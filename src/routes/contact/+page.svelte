<script>
	import { _ } from 'svelte-i18n';
</script>

<div class="page-title-area">
	<div class="container">
		<div class="page-title-content">
			<h2>{$_('contact.page_title')}</h2>
			<ul>
				<li>
					<a href="/"> {$_('contact.breadcrumb_home')} </a>
				</li>
				<li class="active">{$_('contact.breadcrumb_current')}</li>
			</ul>
		</div>
	</div>
	<div class="shape shape-1">
		<img src="/images/page-title-shape-1.png" alt="Image" />
	</div>
	<div class="shape shape-2">
		<img src="/images/page-title-shape-2.png" alt="Image" />
	</div>
</div>

<div class="contact-info-area pt-100 pb-70">
	<div class="container">
		<div class="row">
			<div class="col-lg-4 col-md-6">
				<div class="single-contact-info">
					<i class="ri-map-pin-fill"></i>
					<div class="count-title">
						<p>{$_('contact.address_text')}</p>
					</div>
				</div>
			</div>
			<div class="col-lg-4 col-md-6">
				<div class="single-contact-info">
					<i class="ri-phone-fill"></i>
					<div class="count-title">
						<a href="tel:+****************">{$_('contact.phone_number')}</a>
						<a href="tel:+****************">{$_('contact.phone_number')}</a>
					</div>
				</div>
			</div>
			<div class="col-lg-4 col-md-6 offset-lg-0 offset-md-3">
				<div class="single-contact-info">
					<i class="ri-mail-fill"></i>
					<div class="count-title">
						<a href="mailto: <EMAIL>">{$_('contact.email_address')}</a>
						<a href="mailto: <EMAIL>">{$_('contact.email_address')}</a>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="map-area pb-100">
	<iframe
		src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d51502.423422968604!2d-115.24877995231783!3d36.21758989848515!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80c8eaf574293f1b%3A0x8e720e5b03149745!2sLas%20Vegas%2C%20NV%2089108%2C%20USA!5e0!3m2!1sen!2sbd!4v1612342385496!5m2!1sen!2sbd"
	></iframe>
</div>

<section class="main-contact-area pb-100">
	<div class="container">
		<div class="section-title">
			<h2>{$_('contact.feedback_title')}</h2>
		</div>
		<form id="contactForm">
			<div class="row">
				<div class="col-lg-6 col-sm-6">
					<div class="form-group">
						<label>{$_('contact.name_label')}</label>
						<input
							type="text"
							name="name"
							id="name"
							class="form-control"
							required
							data-error={$_('contact.name_placeholder')}
						/>
						<div class="help-block with-errors"></div>
					</div>
				</div>
				<div class="col-lg-6 col-sm-6">
					<div class="form-group">
						<label>{$_('contact.email_label')}</label>
						<input
							type="email"
							name="email"
							id="email"
							class="form-control"
							required
							data-error={$_('contact.email_placeholder')}
						/>
						<div class="help-block with-errors"></div>
					</div>
				</div>
				<div class="col-lg-6 col-sm-6">
					<div class="form-group">
						<label>{$_('contact.phone_label')}</label>
						<input
							type="text"
							name="phone_number"
							id="phone_number"
							required
							data-error={$_('contact.phone_placeholder')}
							class="form-control"
						/>
						<div class="help-block with-errors"></div>
					</div>
				</div>
				<div class="col-lg-6 col-sm-6">
					<div class="form-group">
						<label>{$_('contact.subject_label')}</label>
						<input
							type="text"
							name="msg_subject"
							id="msg_subject"
							class="form-control"
							required
							data-error={$_('contact.subject_placeholder')}
						/>
						<div class="help-block with-errors"></div>
					</div>
				</div>
				<div class="col-12">
					<div class="form-group">
						<label>{$_('contact.message_label')}</label>
						<textarea
							name="message"
							class="form-control"
							id="message"
							cols="30"
							rows="8"
							required
							data-error={$_('contact.message_placeholder')}
						></textarea>
						<div class="help-block with-errors"></div>
					</div>
				</div>
				<div class="col-lg-12 col-md-12">
					<div class="form-group checkboxs">
						<input type="checkbox" id="chb2" />
						<p>
							{$_('contact.terms_accept')} <a href="terms-conditions.html">{$_('footer.terms_conditions')}</a> {$_('contact.terms_and')}
							<a href="privacy-policy.html">{$_('footer.privacy_policy')}.</a>
						</p>
					</div>
				</div>
				<div class="col-lg-12 col-md-12">
					<button type="submit" class="default-btn">
						<span>
							{$_('contact.send_message')}
							<i class="ri-arrow-right-circle-line"></i>
						</span>
					</button>
					<div id="msgSubmit" class="h3 hidden text-center"></div>
					<div class="clearfix"></div>
				</div>
			</div>
		</form>
	</div>
</section>
