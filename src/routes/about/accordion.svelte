<script lang="ts">
	import { _ } from 'svelte-i18n';

	let activeIndex = 0;

	$: items = [
		{
			title: $_('about.history_title'),
			icon: 'flaticon-contract',
			content: `<p>${$_('about.history_content')}</p>`
		},
		{
			title: $_('about.vision_title'),
			icon: 'flaticon-target',
			content: `<p>${$_('about.vision_content')}</p>`
		},
		{
			title: $_('about.mission_title'),
			icon: 'flaticon-mission',
			content: `<p>${$_('about.mission_content')}</p>`
		}
	];
	function toggle(index: number) {
		activeIndex = index === activeIndex ? -1 : index;
	}
</script>

<div class="faq-accordion">
	<h2>{$_('about.accordion_title')}</h2>
	<ul class="accordion">
		{#each items as item, index}
			<li class="accordion-item">
				<a class="accordion-title {activeIndex === index ? 'active' : ''}">
					<span class={item.icon}></span>
					{item.title}
					<i class="ri-add-fill"></i>
				</a>
				<div class="accordion-content {activeIndex === index ? 'show' : ''}">
					{@html item.content}
				</div>
			</li>
		{/each}
	</ul>
</div>

<style>
	.accordion-content {
		display: none;
	}
	.accordion-content.show {
		display: block;
	}
</style>
